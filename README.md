# حاسبة مواعيد التلقيح للأطفال - Vaccination Calculator

A desktop application for calculating children's vaccination schedules according to the Moroccan National Vaccination Program.

## Features

- **Bilingual Interface**: Arabic (RTL) and French (LTR) support
- **Accurate Date Calculations**: Precise vaccination schedule calculations
- **Market Day Integration**: Find convenient vaccination dates based on weekly market days
- **Modern UI**: Beautiful gradient design with smooth animations
- **Responsive Design**: Works on different screen sizes
- **Offline Functionality**: No internet connection required

## Technology Stack

- **Electron**: Cross-platform desktop application framework
- **HTML/CSS/JavaScript**: Web technologies for the user interface
- **Node.js**: Runtime environment

## Installation

### Prerequisites
- Node.js (version 16 or higher)
- npm (comes with Node.js)

### Development Setup

1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Run in development mode:
   ```bash
   npm run dev
   ```

### Building for Production

1. Build the application:
   ```bash
   npm run build-win
   ```

2. The built application will be in the `dist` folder

## Usage

1. Enter the child's birth date in DD/MM/YYYY format
2. Click "احسب مواعيد التلقيح" (Calculate Vaccination Dates)
3. View the complete vaccination schedule
4. Optionally select a market day to find convenient vaccination dates

## Vaccination Schedule

The application follows the Moroccan National Vaccination Program including:

- Birth: HB1
- 1 month: BCG, VPO0, Vitamin D
- 2 months: Penta1, VPO1, Rota1
- 2.5 months: VPC1 (Pneumo 1)
- 3 months: Penta2, VPO2, Rota2
- 4 months: Penta3, VPO3, Rota3, VPI1
- 4.5 months: VPC2 (Pneumo 2)
- 6 months: VPC3, Vitamin D, Vitamin A
- 9 months: RR1, VPI2, Vitamin A
- 12 months: VPC4 (Pneumo 4)
- 18 months: VPO4, DTC rappel 1, RR2, Vitamin A
- 5 years: VPO5, DTC rappel 2

## Developer

**Jamal Chafik**  
Infirmier Diplômé d'État  
Infirmier Polyvalent

Developed with ❤️ for healthcare professionals

## License

MIT License
